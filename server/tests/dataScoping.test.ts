import request from "supertest";
import mongoose from "mongoose";
import { app } from "../app/app";
import { User } from "../api/models/user.model";
import RestaurantModel from "../api/models/restaurant.model";
import RestaurantOwnership from "../api/models/restaurantOwnership.model";
import constants from "../api/constants";
import bcrypt from "bcrypt";

describe("Data Scoping and Security Tests", () => {
  let superAdminToken: string;
  let franchiseOwner1Token: string;
  let franchiseOwner2Token: string;
  let admin1Token: string;
  let admin2Token: string;
  let user1Token: string;

  let franchiseOwner1: any;
  let franchiseOwner2: any;
  let admin1: any;
  let admin2: any;
  let user1: any;

  let restaurant1: any; // Owned by franchiseOwner1
  let restaurant2: any; // Owned by franchiseOwner1
  let restaurant3: any; // Owned by franchiseOwner2
  let restaurant4: any; // Managed by admin2

  beforeAll(async () => {
    await mongoose.connect(
      process.env.MONGO_TEST || "mongodb://localhost:27017/restaurant_test"
    );

    // Clean up
    await User.deleteMany({});
    await RestaurantModel.deleteMany({});
    await RestaurantOwnership.deleteMany({});
  });

  afterAll(async () => {
    await User.deleteMany({});
    await RestaurantModel.deleteMany({});
    await RestaurantOwnership.deleteMany({});
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Hash password for all test users
    const hashedPassword = await bcrypt.hash("password123", constants.SALT);

    // Create super admin
    const superAdmin = await User.create({
      name: "Super Admin",
      email: "<EMAIL>",
      password: hashedPassword,
      userRole: constants.USERROLE.SUPERADMIN,
      gender:"male",
      isSuperAdmin: true,
      isActive: true,
    });

    // Create franchise owners
    franchiseOwner1 = await User.create({
      name: "Franchise Owner 1",
      email: "<EMAIL>",
      password: hashedPassword,
      userRole: constants.USERROLE.FRANCHISE_OWNER,
      gender:"male",
      isSuperAdmin: false,
      isActive: true,
    });

    franchiseOwner2 = await User.create({
      name: "Franchise Owner 2",
      email: "<EMAIL>",
      password: hashedPassword,
      userRole: constants.USERROLE.FRANCHISE_OWNER,
      gender:"male",
      isSuperAdmin: false,
      isActive: true,
    });

    // Create restaurants
    restaurant1 = await RestaurantModel.create({
      name: "Restaurant 1",
      logo: "logo1.jpg",
      address: "123 Street 1",
      phone: "+1111111111",
      description: "Restaurant 1",
      subStart: new Date(),
      subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      isActive: true,
      currentOwner: franchiseOwner1._id,
    });

    restaurant2 = await RestaurantModel.create({
      name: "Restaurant 2",
      logo: "logo2.jpg",
      address: "123 Street 2",
      phone: "+2222222222",
      description: "Restaurant 2",
      subStart: new Date(),
      subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      isActive: true,
      currentOwner: franchiseOwner1._id,
    });

    restaurant3 = await RestaurantModel.create({
      name: "Restaurant 3",
      logo: "logo3.jpg",
      address: "123 Street 3",
      phone: "+3333333333",
      description: "Restaurant 3",
      subStart: new Date(),
      subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      isActive: true,
      currentOwner: franchiseOwner2._id,
    });

    restaurant4 = await RestaurantModel.create({
      name: "Restaurant 4",
      logo: "logo4.jpg",
      address: "123 Street 4",
      phone: "+4444444444",
      description: "Restaurant 4",
      subStart: new Date(),
      subEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      isActive: true,
    });

    // Create admins
    admin1 = await User.create({
      name: "Admin 1",
      email: "<EMAIL>",
      password: hashedPassword,
      userRole: constants.USERROLE.ADMIN,
      gender:"male",
      restaurant: restaurant1._id,
      isSuperAdmin: false,
      isActive: true,
    });

    admin2 = await User.create({
      name: "Admin 2",
      email: "<EMAIL>",
      password: hashedPassword,
      userRole: constants.USERROLE.ADMIN,
      gender:"male",
      restaurant: restaurant4._id,
      isSuperAdmin: false,
      isActive: true,
    });

    // Create users
    user1 = await User.create({
      name: "User 1",
      email: "<EMAIL>",
      password: hashedPassword,
      userRole: constants.USERROLE.USER,
      gender:"male",
      restaurant: restaurant1._id,
      isSuperAdmin: false,
      isActive: true,
    });

    // Update franchise owners with owned restaurants
    franchiseOwner1.ownedRestaurants = [restaurant1._id, restaurant2._id];
    await franchiseOwner1.save();

    franchiseOwner2.ownedRestaurants = [restaurant3._id];
    await franchiseOwner2.save();

    // Create ownership records
    await RestaurantOwnership.create({
      owner: franchiseOwner1._id,
      restaurant: restaurant1._id,
      ownershipType: "full",
      ownershipPercentage: 100,
      isActive: true,
    });

    await RestaurantOwnership.create({
      owner: franchiseOwner1._id,
      restaurant: restaurant2._id,
      ownershipType: "full",
      ownershipPercentage: 100,
      isActive: true,
    });

    await RestaurantOwnership.create({
      owner: franchiseOwner2._id,
      restaurant: restaurant3._id,
      ownershipType: "full",
      ownershipPercentage: 100,
      isActive: true,
    });

    // Get authentication tokens
    const superAdminLogin = await request(app)
      .post("/api/v1/en/auth/login")
      .send({ email: "<EMAIL>", password: "password123" });
    superAdminToken = superAdminLogin.body.data.token;

    const franchiseOwner1Login = await request(app)
      .post("/api/v1/en/auth/login")
      .send({ email: "<EMAIL>", password: "password123" });
    franchiseOwner1Token = franchiseOwner1Login.body.data.token;

    const franchiseOwner2Login = await request(app)
      .post("/api/v1/en/auth/login")
      .send({ email: "<EMAIL>", password: "password123" });
    franchiseOwner2Token = franchiseOwner2Login.body.data.token;

    const admin1Login = await request(app)
      .post("/api/v1/en/auth/login")
      .send({ email: "<EMAIL>", password: "password123" });
    admin1Token = admin1Login.body.data.token;

    const admin2Login = await request(app)
      .post("/api/v1/en/auth/login")
      .send({ email: "<EMAIL>", password: "password123" });
    admin2Token = admin2Login.body.data.token;

    const user1Login = await request(app)
      .post("/api/v1/en/auth/login")
      .send({ email: "<EMAIL>", password: "password123" });
    user1Token = user1Login.body.data.token;
  });

  afterEach(async () => {
    await User.deleteMany({});
    await RestaurantModel.deleteMany({});
    await RestaurantOwnership.deleteMany({});
  });

  describe("Restaurant Access Control", () => {
    it("super admin should access all restaurants", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${superAdminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(4);
    });

    it("franchise owner 1 should only access owned restaurants", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(2);

      const restaurantIds = response.body.data.map((r: any) => r._id);
      expect(restaurantIds).toContain(restaurant1._id.toString());
      expect(restaurantIds).toContain(restaurant2._id.toString());
      expect(restaurantIds).not.toContain(restaurant3._id.toString());
      expect(restaurantIds).not.toContain(restaurant4._id.toString());
    });

    it("franchise owner 2 should only access owned restaurants", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${franchiseOwner2Token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(1);
      expect(response.body.data[0]._id).toBe(restaurant3._id.toString());
    });

    it("admin should not access restaurant list endpoint", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${admin1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it("regular user should not access restaurant list endpoint", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", `Bearer ${user1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe("Individual Restaurant Access", () => {
    it("franchise owner should access owned restaurant", async () => {
      const response = await request(app)
        .get(`/api/v1/en/restaurant/${restaurant1._id}`)
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data._id).toBe(restaurant1._id.toString());
    });

    it("franchise owner should not access non-owned restaurant", async () => {
      const response = await request(app)
        .get(`/api/v1/en/restaurant/${restaurant3._id}`)
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it("admin should access assigned restaurant", async () => {
      const response = await request(app)
        .get(`/api/v1/en/restaurant/${restaurant1._id}`)
        .set("Authorization", `Bearer ${admin1Token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data._id).toBe(restaurant1._id.toString());
    });

    it("admin should not access non-assigned restaurant", async () => {
      const response = await request(app)
        .get(`/api/v1/en/restaurant/${restaurant2._id}`)
        .set("Authorization", `Bearer ${admin1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it("user should access assigned restaurant", async () => {
      const response = await request(app)
        .get(`/api/v1/en/restaurant/${restaurant1._id}`)
        .set("Authorization", `Bearer ${user1Token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data._id).toBe(restaurant1._id.toString());
    });

    it("user should not access non-assigned restaurant", async () => {
      const response = await request(app)
        .get(`/api/v1/en/restaurant/${restaurant2._id}`)
        .set("Authorization", `Bearer ${user1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe("Ownership Access Control", () => {
    it("franchise owner should access own ownerships", async () => {
      const response = await request(app)
        .get(`/api/v1/en/ownership/owner/${franchiseOwner1._id}`)
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(2);
    });

    it("franchise owner should not access other ownerships", async () => {
      const response = await request(app)
        .get(`/api/v1/en/ownership/owner/${franchiseOwner2._id}`)
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it("admin should not access ownership information", async () => {
      const response = await request(app)
        .get(`/api/v1/en/ownership/owner/${franchiseOwner1._id}`)
        .set("Authorization", `Bearer ${admin1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe("Cross-Tenant Data Isolation", () => {
    it("should ensure complete data isolation between franchise owners", async () => {
      // Franchise Owner 1 dashboard should not show Franchise Owner 2's data
      const response1 = await request(app)
        .get("/api/v1/en/franchiseOwner/dashboard")
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response1.status).toBe(200);
      expect(response1.body.data.restaurants.length).toBe(2);
      expect(
        response1.body.data.restaurants.every(
          (r: any) => r.currentOwner._id === franchiseOwner1._id.toString()
        )
      ).toBe(true);

      // Franchise Owner 2 dashboard should not show Franchise Owner 1's data
      const response2 = await request(app)
        .get("/api/v1/en/franchiseOwner/dashboard")
        .set("Authorization", `Bearer ${franchiseOwner2Token}`);

      expect(response2.status).toBe(200);
      expect(response2.body.data.restaurants.length).toBe(1);
      expect(response2.body.data.restaurants[0].currentOwner._id).toBe(
        franchiseOwner2._id.toString()
      );
    });

    it("should prevent unauthorized ownership transfers", async () => {
      const transferData = {
        currentOwnerId: franchiseOwner2._id.toString(),
        newOwnerId: franchiseOwner1._id.toString(),
        restaurantId: restaurant3._id.toString(),
      };

      // Franchise Owner 1 should not be able to transfer Franchise Owner 2's restaurant
      const response = await request(app)
        .post("/api/v1/en/ownership/transfer")
        .set("Authorization", `Bearer ${franchiseOwner1Token}`)
        .send(transferData);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it("should prevent unauthorized franchise access", async () => {
      // Create franchise for franchiseOwner2
      const franchise2 = await request(app)
        .post("/api/v1/en/franchise")
        .set("Authorization", `Bearer ${franchiseOwner2Token}`)
        .send({
          name: "Franchise 2",
          brandName: "Brand 2",
          ownerId: franchiseOwner2._id.toString(),
        });

      // Franchise Owner 1 should not access Franchise Owner 2's franchise
      const response = await request(app)
        .get(`/api/v1/en/franchise/${franchise2.body.data._id}`)
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });
  });

  describe("Role-Based Access Control", () => {
    it("should enforce proper role hierarchy", async () => {
      // Regular user should not access franchise owner endpoints
      const response1 = await request(app)
        .get("/api/v1/en/franchiseOwner/dashboard")
        .set("Authorization", `Bearer ${user1Token}`);

      expect(response1.status).toBe(403);

      // Admin should not access franchise owner endpoints
      const response2 = await request(app)
        .get("/api/v1/en/franchiseOwner/dashboard")
        .set("Authorization", `Bearer ${admin1Token}`);

      expect(response2.status).toBe(403);

      // Franchise owner should not access super admin only endpoints
      const response3 = await request(app)
        .get("/api/v1/en/ownership/")
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response3.status).toBe(403);
    });

    it("should allow super admin to access all data", async () => {
      // Super admin should access all ownerships
      const response1 = await request(app)
        .get("/api/v1/en/ownership/")
        .set("Authorization", `Bearer ${superAdminToken}`);

      expect(response1.status).toBe(200);
      expect(response1.body.data.length).toBe(3);

      // Super admin should access all franchises
      const response2 = await request(app)
        .get("/api/v1/en/franchise")
        .set("Authorization", `Bearer ${superAdminToken}`);

      expect(response2.status).toBe(200);
    });
  });

  describe("Authentication and Authorization", () => {
    it("should reject requests without authentication", async () => {
      const response = await request(app).get("/api/v1/en/restaurant");

      expect(response.status).toBe(401);
    });

    it("should reject requests with invalid tokens", async () => {
      const response = await request(app)
        .get("/api/v1/en/restaurant")
        .set("Authorization", "Bearer invalid-token");

      expect(response.status).toBe(400);
    });

    it("should include proper user context in JWT tokens", async () => {
      // Verify franchise owner token includes owned restaurants
      const response = await request(app)
        .get("/api/v1/en/franchiseOwner/dashboard")
        .set("Authorization", `Bearer ${franchiseOwner1Token}`);

      expect(response.status).toBe(200);
      expect(response.body.data.restaurants.length).toBe(2);
    });
  });
});
