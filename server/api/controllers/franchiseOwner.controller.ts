import { Response } from "express";
import { AuthenticatedRequest } from "../types/common.types";
import { formatResponse } from "../utilities/formatRes";
import { User } from "../models/user.model";
import RestaurantModel from "../models/restaurant.model";
import RestaurantOwnership from "../models/restaurantOwnership.model";
import Franchise from "../models/franchise.model";
import Logger from "../../utils/logUtils";
import constants from "../constants";

const FranchiseOwnerController = {
  // Get franchise owner dashboard data
  getDashboard: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const ownerId = req.user.id;

      // SECURITY FIX: Get owned restaurants through ownership records for better data isolation
      // First get active ownership records to ensure data integrity
      const activeOwnerships = await RestaurantOwnership.find({
        owner: ownerId,
        isActive: true,
        $or: [{ endDate: null }, { endDate: { $gte: new Date() } }],
      }).populate('restaurant', 'name address phone franchise isActive');

      // Filter out inactive restaurants and extract restaurant IDs
      const ownedRestaurantIds = activeOwnerships
        .filter(ownership => ownership.restaurant && ownership.restaurant.isActive)
        .map(ownership => ownership.restaurant._id);

      // Get owned restaurants with proper validation
      const ownedRestaurants = await RestaurantModel.find({
        _id: { $in: ownedRestaurantIds },
        currentOwner: ownerId,
        isActive: true,
      }).populate('franchise', 'name brandName');

      // Get franchise information
      const franchise = await Franchise.findOne({
        owner: ownerId,
        isActive: true,
      }).populate('restaurants', 'name address phone');

      // Use the already fetched ownership records
      const ownerships = activeOwnerships;

      // Calculate summary statistics
      const totalRestaurants = ownedRestaurants.length;
      const activeRestaurants = ownedRestaurants.filter(r => r.isActive).length;
      const franchiseRestaurants = ownedRestaurants.filter(r => r.restaurantType === 'franchise').length;
      const independentRestaurants = ownedRestaurants.filter(r => r.restaurantType === 'independent').length;

      const dashboardData = {
        summary: {
          totalRestaurants,
          activeRestaurants,
          franchiseRestaurants,
          independentRestaurants,
        },
        restaurants: ownedRestaurants,
        franchise: franchise,
        ownerships: ownerships,
      };

      formatResponse(res, 200, true, "Dashboard data retrieved successfully", dashboardData);
    } catch (error) {
      Logger.error("Error fetching franchise owner dashboard:", error);
      formatResponse(res, 500, false, "Error fetching dashboard data", error);
    }
  },

  // Get all restaurants owned by the franchise owner
  getMyRestaurants: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER) {
        return formatResponse(res, 403, false, "Access denied");
      }

      // SECURITY FIX: Verify ownership through ownership records for better data isolation
      const activeOwnerships = await RestaurantOwnership.find({
        owner: req.user.id,
        isActive: true,
        $or: [{ endDate: null }, { endDate: { $gte: new Date() } }],
      }).populate('restaurant');

      // Extract restaurant IDs that the user actually owns
      const ownedRestaurantIds = activeOwnerships
        .filter(ownership => ownership.restaurant && ownership.restaurant.isActive)
        .map(ownership => ownership.restaurant._id);

      const restaurants = await RestaurantModel.find({
        _id: { $in: ownedRestaurantIds },
        currentOwner: req.user.id,
        isActive: true,
      })
        .populate('franchise', 'name brandName')
        .populate('currentOwner', 'name email');

      formatResponse(res, 200, true, "Restaurants retrieved successfully", restaurants);
    } catch (error) {
      Logger.error("Error fetching franchise owner restaurants:", error);
      formatResponse(res, 500, false, "Error fetching restaurants", error);
    }
  },

  // Get franchise owner's franchise
  getMyFranchise: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const franchise = await Franchise.findOne({
        owner: req.user.id,
        isActive: true,
      })
        .populate('restaurants', 'name address phone')
        .populate('owner', 'name email');

      if (!franchise) {
        return formatResponse(res, 404, false, "Franchise not found");
      }

      formatResponse(res, 200, true, "Franchise retrieved successfully", franchise);
    } catch (error) {
      Logger.error("Error fetching franchise:", error);
      formatResponse(res, 500, false, "Error fetching franchise", error);
    }
  },

  // Get ownership records for the franchise owner
  getMyOwnerships: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const ownerships = await RestaurantOwnership.find({
        owner: req.user.id,
        isActive: true,
      })
        .populate('restaurant', 'name address phone')
        .populate('transferredFrom', 'name email')
        .populate('transferredTo', 'name email');

      formatResponse(res, 200, true, "Ownerships retrieved successfully", ownerships);
    } catch (error) {
      Logger.error("Error fetching ownerships:", error);
      formatResponse(res, 500, false, "Error fetching ownerships", error);
    }
  },

  // Get users/staff for franchise owner's restaurants
  getMyStaff: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER) {
        return formatResponse(res, 403, false, "Access denied");
      }

      // Get all restaurants owned by this franchise owner
      const ownedRestaurants = await RestaurantModel.find({
        currentOwner: req.user.id,
        isActive: true,
      }).select('_id');

      const restaurantIds = ownedRestaurants.map(r => r._id);

      // Get all staff working in these restaurants
      const staff = await User.find({
        restaurant: { $in: restaurantIds },
        userRole: { $ne: constants.USERROLE.FRANCHISE_OWNER },
        isActive: true,
      })
        .populate('restaurant', 'name address')
        .populate('position', 'name')
        .populate('empRole', 'name')
        .populate('department', 'name')
        .select('-password');

      formatResponse(res, 200, true, "Staff retrieved successfully", staff);
    } catch (error) {
      Logger.error("Error fetching staff:", error);
      formatResponse(res, 500, false, "Error fetching staff", error);
    }
  },

  // Get analytics for franchise owner's restaurants
  getAnalytics: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const ownerId = req.user.id;

      // Get owned restaurants with basic stats
      const restaurants = await RestaurantModel.find({
        currentOwner: ownerId,
        isActive: true,
      });

      // Calculate analytics
      const analytics = {
        totalRestaurants: restaurants.length,
        restaurantsByType: {
          franchise: restaurants.filter(r => r.restaurantType === 'franchise').length,
          independent: restaurants.filter(r => r.restaurantType === 'independent').length,
          chain: restaurants.filter(r => r.restaurantType === 'chain').length,
        },
        restaurantsByLocation: restaurants.reduce((acc: any, restaurant) => {
          const city = restaurant.location?.city || 'Unknown';
          acc[city] = (acc[city] || 0) + 1;
          return acc;
        }, {}),
        activeSubscriptions: restaurants.filter(r => r.subEnd > new Date()).length,
        expiredSubscriptions: restaurants.filter(r => r.subEnd <= new Date()).length,
      };

      formatResponse(res, 200, true, "Analytics retrieved successfully", analytics);
    } catch (error) {
      Logger.error("Error fetching analytics:", error);
      formatResponse(res, 500, false, "Error fetching analytics", error);
    }
  },

  // Update franchise owner profile
  updateProfile: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (req.user?.userRole !== constants.USERROLE.FRANCHISE_OWNER) {
        return formatResponse(res, 403, false, "Access denied");
      }

      const { name, phone, address, profileImg } = req.body;

      const user = await User.findById(req.user.id);
      if (!user) {
        return formatResponse(res, 404, false, "User not found");
      }

      // Update allowed fields
      if (name) user.name = name;
      if (phone) user.phone = phone;
      if (address) user.address = address;
      if (profileImg) user.profileImg = profileImg;

      await user.save();

      const { password, ...userResponse } = user.toObject();
      formatResponse(res, 200, true, "Profile updated successfully", userResponse);
    } catch (error) {
      Logger.error("Error updating profile:", error);
      formatResponse(res, 500, false, "Error updating profile", error);
    }
  },
};

export default FranchiseOwnerController;
